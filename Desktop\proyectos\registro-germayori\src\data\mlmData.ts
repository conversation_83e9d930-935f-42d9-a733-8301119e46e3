import { User, Commission, CompensationPlan, UserRank } from '../types/mlm';

// Plan de compensación
export const compensationPlan: CompensationPlan = {
  directCommissionRate: 0.15, // 15%
  binaryCommissionRate: 0.10, // 10%
  spilloverCommissionRate: 0.05, // 5%
  leadershipBonusRates: {
    distributor: 0.02, // 2%
    supervisor: 0.03, // 3%
    manager: 0.05, // 5%
    director: 0.07, // 7%
    executive: 0.10, // 10%
    diamond: 0.15, // 15%
  },
  rankRequirements: {
    distributor: {
      personalSales: 0,
      groupSales: 0,
      directReferrals: 0,
      teamSize: 0,
    },
    supervisor: {
      personalSales: 5000,
      groupSales: 15000,
      directReferrals: 3,
      teamSize: 10,
    },
    manager: {
      personalSales: 10000,
      groupSales: 50000,
      directReferrals: 5,
      teamSize: 25,
    },
    director: {
      personalSales: 20000,
      groupSales: 100000,
      directReferrals: 8,
      teamSize: 50,
    },
    executive: {
      personalSales: 35000,
      groupSales: 250000,
      directReferrals: 12,
      teamSize: 100,
    },
    diamond: {
      personalSales: 50000,
      groupSales: 500000,
      directReferrals: 20,
      teamSize: 200,
    },
  },
};

// Datos de ejemplo de usuarios
export const sampleUsers: User[] = [
  {
    id: '1',
    name: 'Carlos Germayori',
    email: '<EMAIL>',
    phone: '+1234567890',
    isActive: true,
    joinDate: new Date('2024-01-01'),
    level: 0,
    position: 'center',
    personalSales: 50000,
    groupSales: 500000,
    rank: 'diamond',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 200,
  },
  {
    id: '2',
    name: 'María González',
    email: '<EMAIL>',
    phone: '+1234567891',
    isActive: true,
    joinDate: new Date('2024-01-15'),
    sponsorId: '1',
    level: 1,
    position: 'left',
    personalSales: 25000,
    groupSales: 150000,
    rank: 'executive',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 75,
  },
  {
    id: '3',
    name: 'Juan Pérez',
    email: '<EMAIL>',
    phone: '+1234567892',
    isActive: true,
    joinDate: new Date('2024-01-20'),
    sponsorId: '1',
    level: 1,
    position: 'right',
    personalSales: 20000,
    groupSales: 120000,
    rank: 'director',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 60,
  },
  {
    id: '4',
    name: 'Ana López',
    email: '<EMAIL>',
    phone: '+1234567893',
    isActive: false,
    joinDate: new Date('2024-02-01'),
    sponsorId: '2',
    level: 2,
    position: 'left',
    personalSales: 8000,
    groupSales: 25000,
    rank: 'supervisor',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 15,
  },
  {
    id: '5',
    name: 'Roberto Silva',
    email: '<EMAIL>',
    phone: '+1234567894',
    isActive: true,
    joinDate: new Date('2024-02-05'),
    sponsorId: '2',
    level: 2,
    position: 'right',
    personalSales: 12000,
    groupSales: 40000,
    rank: 'manager',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 25,
  },
  {
    id: '6',
    name: 'Laura Martínez',
    email: '<EMAIL>',
    phone: '+1234567895',
    isActive: true,
    joinDate: new Date('2024-02-10'),
    sponsorId: '3',
    level: 2,
    position: 'left',
    personalSales: 15000,
    groupSales: 35000,
    rank: 'manager',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 20,
  },
  {
    id: '7',
    name: 'Diego Ramírez',
    email: '<EMAIL>',
    phone: '+1234567896',
    isActive: false,
    joinDate: new Date('2024-02-15'),
    sponsorId: '3',
    level: 2,
    position: 'right',
    personalSales: 5000,
    groupSales: 15000,
    rank: 'distributor',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 8,
  },
  {
    id: '8',
    name: 'Carmen Flores',
    email: '<EMAIL>',
    phone: '+1234567897',
    isActive: true,
    joinDate: new Date('2024-02-20'),
    sponsorId: '4',
    level: 3,
    position: 'left',
    personalSales: 3000,
    groupSales: 8000,
    rank: 'distributor',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 3,
  },
  {
    id: '9',
    name: 'Fernando Torres',
    email: '<EMAIL>',
    phone: '+1234567898',
    isActive: true,
    joinDate: new Date('2024-02-25'),
    sponsorId: '5',
    level: 3,
    position: 'left',
    personalSales: 7000,
    groupSales: 12000,
    rank: 'supervisor',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 5,
  },
  {
    id: '10',
    name: 'Patricia Vega',
    email: '<EMAIL>',
    phone: '+1234567899',
    isActive: true,
    joinDate: new Date('2024-03-01'),
    sponsorId: '6',
    level: 3,
    position: 'right',
    personalSales: 9000,
    groupSales: 18000,
    rank: 'supervisor',
    commissions: [],
    directReferrals: [],
    totalTeamSize: 7,
  },
];

// Comisiones de ejemplo
export const sampleCommissions: Commission[] = [
  {
    id: 'c1',
    userId: '1',
    type: 'direct',
    amount: 2250,
    date: new Date('2024-03-01'),
    fromUserId: '2',
    level: 1,
    isPaid: true,
    paymentDate: new Date('2024-03-05'),
  },
  {
    id: 'c2',
    userId: '1',
    type: 'spillover',
    amount: 1000,
    date: new Date('2024-03-02'),
    fromUserId: '5',
    level: 2,
    isPaid: false,
  },
  {
    id: 'c3',
    userId: '2',
    type: 'direct',
    amount: 1800,
    date: new Date('2024-03-03'),
    fromUserId: '4',
    level: 1,
    isPaid: false,
  },
  {
    id: 'c4',
    userId: '2',
    type: 'leadership',
    amount: 750,
    date: new Date('2024-03-04'),
    fromUserId: '5',
    level: 1,
    isPaid: false,
  },
  {
    id: 'c5',
    userId: '3',
    type: 'binary',
    amount: 1200,
    date: new Date('2024-03-05'),
    fromUserId: '6',
    level: 1,
    isPaid: true,
    paymentDate: new Date('2024-03-10'),
  },
];
