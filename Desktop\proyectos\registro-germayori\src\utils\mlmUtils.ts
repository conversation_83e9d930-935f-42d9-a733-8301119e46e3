import { User, Commission, NetworkStats, PaymentSummary, UserRank, CompensationPlan } from '../types/mlm';

// Calcular estadísticas de la red
export function calculateNetworkStats(users: User[], commissions: Commission[]): NetworkStats {
  const activeUsers = users.filter(user => user.isActive).length;
  const inactiveUsers = users.filter(user => !user.isActive).length;
  const totalSales = users.reduce((sum, user) => sum + user.personalSales, 0);
  const totalCommissions = commissions.reduce((sum, commission) => sum + commission.amount, 0);
  const pendingPayments = commissions
    .filter(commission => !commission.isPaid)
    .reduce((sum, commission) => sum + commission.amount, 0);
  
  const topPerformers = users
    .filter(user => user.isActive)
    .sort((a, b) => b.personalSales - a.personalSales)
    .slice(0, 5);

  return {
    totalUsers: users.length,
    activeUsers,
    inactiveUsers,
    totalSales,
    totalCommissions,
    pendingPayments,
    topPerformers,
  };
}

// Obtener usuarios por patrocinador
export function getUsersBySponsor(users: User[], sponsorId: string): User[] {
  return users.filter(user => user.sponsorId === sponsorId);
}

// Construir árbol genealógico
export function buildMLMTree(users: User[], rootId?: string): User[] {
  const userMap = new Map(users.map(user => [user.id, { ...user, directReferrals: [] }]));
  const rootUsers: User[] = [];

  users.forEach(user => {
    if (user.sponsorId && userMap.has(user.sponsorId)) {
      const sponsor = userMap.get(user.sponsorId)!;
      const userWithReferrals = userMap.get(user.id)!;
      sponsor.directReferrals.push(userWithReferrals);
    } else if (!user.sponsorId || (rootId && user.id === rootId)) {
      rootUsers.push(userMap.get(user.id)!);
    }
  });

  return rootId ? rootUsers.filter(user => user.id === rootId) : rootUsers;
}

// Calcular rango basado en requisitos
export function calculateUserRank(user: User, plan: CompensationPlan): UserRank {
  const ranks: UserRank[] = ['diamond', 'executive', 'director', 'manager', 'supervisor', 'distributor'];
  
  for (const rank of ranks) {
    const requirements = plan.rankRequirements[rank];
    if (
      user.personalSales >= requirements.personalSales &&
      user.groupSales >= requirements.groupSales &&
      user.directReferrals.length >= requirements.directReferrals &&
      user.totalTeamSize >= requirements.teamSize
    ) {
      return rank;
    }
  }
  
  return 'distributor';
}

// Calcular comisiones para un usuario
export function calculateCommissions(
  user: User,
  sale: { amount: number; fromUserId: string; level: number },
  plan: CompensationPlan
): Commission[] {
  const commissions: Commission[] = [];
  const now = new Date();

  // Comisión directa (solo para referidos directos)
  if (sale.level === 1) {
    commissions.push({
      id: `direct_${user.id}_${Date.now()}`,
      userId: user.id,
      type: 'direct',
      amount: sale.amount * plan.directCommissionRate,
      date: now,
      fromUserId: sale.fromUserId,
      level: sale.level,
      isPaid: false,
    });
  }

  // Comisión binaria (para estructura binaria)
  if (sale.level <= 5) {
    commissions.push({
      id: `binary_${user.id}_${Date.now()}`,
      userId: user.id,
      type: 'binary',
      amount: sale.amount * plan.binaryCommissionRate * (0.8 ** (sale.level - 1)),
      date: now,
      fromUserId: sale.fromUserId,
      level: sale.level,
      isPaid: false,
    });
  }

  // Comisión de derrame (spillover)
  if (sale.level > 1 && sale.level <= 3) {
    commissions.push({
      id: `spillover_${user.id}_${Date.now()}`,
      userId: user.id,
      type: 'spillover',
      amount: sale.amount * plan.spilloverCommissionRate,
      date: now,
      fromUserId: sale.fromUserId,
      level: sale.level,
      isPaid: false,
    });
  }

  // Bonus de liderazgo
  const leadershipRate = plan.leadershipBonusRates[user.rank];
  if (leadershipRate > 0) {
    commissions.push({
      id: `leadership_${user.id}_${Date.now()}`,
      userId: user.id,
      type: 'leadership',
      amount: sale.amount * leadershipRate,
      date: now,
      fromUserId: sale.fromUserId,
      level: sale.level,
      isPaid: false,
    });
  }

  return commissions;
}

// Generar resumen de pagos
export function generatePaymentSummary(users: User[], commissions: Commission[]): PaymentSummary[] {
  return users.map(user => {
    const userCommissions = commissions.filter(c => c.userId === user.id);
    const directCommissions = userCommissions
      .filter(c => c.type === 'direct')
      .reduce((sum, c) => sum + c.amount, 0);
    const spilloverCommissions = userCommissions
      .filter(c => c.type === 'spillover')
      .reduce((sum, c) => sum + c.amount, 0);
    const leadershipBonus = userCommissions
      .filter(c => c.type === 'leadership')
      .reduce((sum, c) => sum + c.amount, 0);
    const totalCommissions = userCommissions.reduce((sum, c) => sum + c.amount, 0);
    
    const allPaid = userCommissions.length > 0 && userCommissions.every(c => c.isPaid);
    const latestPayment = userCommissions
      .filter(c => c.isPaid && c.paymentDate)
      .sort((a, b) => (b.paymentDate?.getTime() || 0) - (a.paymentDate?.getTime() || 0))[0];

    return {
      userId: user.id,
      userName: user.name,
      totalCommissions,
      directCommissions,
      spilloverCommissions,
      leadershipBonus,
      isPaid: allPaid,
      paymentDate: latestPayment?.paymentDate,
    };
  }).filter(summary => summary.totalCommissions > 0);
}

// Formatear moneda
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

// Obtener color por rango
export function getRankColor(rank: UserRank): string {
  const colors = {
    distributor: '#6B7280',
    supervisor: '#10B981',
    manager: '#3B82F6',
    director: '#8B5CF6',
    executive: '#F59E0B',
    diamond: '#EF4444',
  };
  return colors[rank];
}

// Obtener nombre del rango en español
export function getRankName(rank: UserRank): string {
  const names = {
    distributor: 'Distribuidor',
    supervisor: 'Supervisor',
    manager: 'Gerente',
    director: 'Director',
    executive: 'Ejecutivo',
    diamond: 'Diamante',
  };
  return names[rank];
}
