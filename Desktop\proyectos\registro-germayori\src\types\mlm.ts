// Tipos para el sistema MLM de Registro Germayori

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  isActive: boolean;
  joinDate: Date;
  sponsorId?: string; // ID del patrocinador directo
  level: number; // Nivel en la estructura
  position: 'left' | 'right' | 'center'; // Posición en el árbol binario
  personalSales: number;
  groupSales: number;
  rank: UserRank;
  commissions: Commission[];
  directReferrals: User[];
  totalTeamSize: number;
}

export interface Commission {
  id: string;
  userId: string;
  type: CommissionType;
  amount: number;
  date: Date;
  fromUserId: string; // De quién viene la comisión
  level: number; // En qué nivel se generó
  isPaid: boolean;
  paymentDate?: Date;
}

export type CommissionType = 
  | 'direct' // Comisión directa
  | 'binary' // Comisión binaria
  | 'spillover' // Derrame
  | 'leadership' // Liderazgo
  | 'bonus'; // Bonos especiales

export type UserRank = 
  | 'distributor' // Distribuidor
  | 'supervisor' // Supervisor
  | 'manager' // Gerente
  | 'director' // Director
  | 'executive' // Ejecutivo
  | 'diamond'; // Diamante

export interface MLMStructure {
  users: User[];
  totalActiveUsers: number;
  totalInactiveUsers: number;
  totalCommissions: number;
  pendingPayments: number;
}

export interface PaymentSummary {
  userId: string;
  userName: string;
  totalCommissions: number;
  directCommissions: number;
  spilloverCommissions: number;
  leadershipBonus: number;
  isPaid: boolean;
  paymentDate?: Date;
}

export interface NetworkStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  totalSales: number;
  totalCommissions: number;
  pendingPayments: number;
  topPerformers: User[];
}

// Configuración del plan de compensación
export interface CompensationPlan {
  directCommissionRate: number; // % de comisión directa
  binaryCommissionRate: number; // % de comisión binaria
  spilloverCommissionRate: number; // % de comisión por derrame
  leadershipBonusRates: Record<UserRank, number>; // % de bonus por rango
  rankRequirements: Record<UserRank, {
    personalSales: number;
    groupSales: number;
    directReferrals: number;
    teamSize: number;
  }>;
}
