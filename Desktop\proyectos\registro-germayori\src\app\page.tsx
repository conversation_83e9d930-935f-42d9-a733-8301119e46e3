'use client';

import { useState, useEffect } from 'react';
import { User, Commission } from '../types/mlm';
import { sampleUsers, sampleCommissions } from '../data/mlmData';

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [users, setUsers] = useState<User[]>([]);
  const [commissions, setCommissions] = useState<Commission[]>([]);

  useEffect(() => {
    // Cargar datos iniciales
    setUsers(sampleUsers);
    setCommissions(sampleCommissions);
  }, []);

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'users', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '👥' },
    { id: 'network', name: 'Red MLM', icon: '🌳' },
    { id: 'payments', name: 'Pagos', icon: '💰' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Registro Germayori</h1>
              <span className="ml-2 text-sm text-gray-500">Sistema MLM</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Admin Panel</span>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                CG
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-sm font-medium text-gray-600">Total Usuarios</h3>
                <p className="text-2xl font-semibold text-blue-600">{users.length}</p>
                <p className="text-sm text-gray-500">
                  {users.filter(u => u.isActive).length} activos, {users.filter(u => !u.isActive).length} inactivos
                </p>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-sm font-medium text-gray-600">Ventas Totales</h3>
                <p className="text-2xl font-semibold text-green-600">
                  ${users.reduce((sum, u) => sum + u.personalSales, 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">Ventas acumuladas</p>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-sm font-medium text-gray-600">Comisiones Totales</h3>
                <p className="text-2xl font-semibold text-purple-600">
                  ${commissions.reduce((sum, c) => sum + c.amount, 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">Comisiones generadas</p>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-sm font-medium text-gray-600">Pagos Pendientes</h3>
                <p className="text-2xl font-semibold text-yellow-600">
                  ${commissions.filter(c => !c.isPaid).reduce((sum, c) => sum + c.amount, 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">Por pagar</p>
              </div>
            </div>

            {/* Users List */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Usuarios Recientes</h3>
              </div>
              <div className="divide-y divide-gray-200">
                {users.slice(0, 5).map((user) => (
                  <div key={user.id} className="px-6 py-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                        <span className="text-sm font-medium text-gray-600">
                          {user.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{user.name}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        ${user.personalSales.toLocaleString()}
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {user.isActive ? 'Activo' : 'Inactivo'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Gestión de Usuarios</h2>
            <p className="text-gray-600">Funcionalidad de usuarios en desarrollo...</p>
          </div>
        )}

        {activeTab === 'network' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Red MLM</h2>
            <p className="text-gray-600">Visualización de red en desarrollo...</p>
          </div>
        )}

        {activeTab === 'payments' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Gestión de Pagos</h2>
            <p className="text-gray-600">Sistema de pagos en desarrollo...</p>
          </div>
        )}
      </main>
    </div>
  );
}
