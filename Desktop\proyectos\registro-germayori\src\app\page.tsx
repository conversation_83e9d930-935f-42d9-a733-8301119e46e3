'use client';

import { useState, useEffect } from 'react';
import Dashboard from '../components/Dashboard';
import UserManagement from '../components/UserManagement';
import NetworkTree from '../components/NetworkTree';
import PaymentManagement from '../components/PaymentManagement';
import { User, Commission } from '../types/mlm';
import { sampleUsers, sampleCommissions } from '../data/mlmData';

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [users, setUsers] = useState<User[]>([]);
  const [commissions, setCommissions] = useState<Commission[]>([]);

  useEffect(() => {
    // Cargar datos iniciales
    setUsers(sampleUsers);
    setCommissions(sampleCommissions);
  }, []);

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'users', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '👥' },
    { id: 'network', name: 'Red MLM', icon: '🌳' },
    { id: 'payments', name: 'Pagos', icon: '💰' },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard users={users} commissions={commissions} />;
      case 'users':
        return <UserManagement users={users} setUsers={setUsers} />;
      case 'network':
        return <NetworkTree users={users} />;
      case 'payments':
        return <PaymentManagement users={users} commissions={commissions} setCommissions={setCommissions} />;
      default:
        return <Dashboard users={users} commissions={commissions} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Registro Germayori</h1>
              <span className="ml-2 text-sm text-gray-500">Sistema MLM</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Admin Panel</span>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                CG
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {renderContent()}
      </main>
    </div>
  );
}
